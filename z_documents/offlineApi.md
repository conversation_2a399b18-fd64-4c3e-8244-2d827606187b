我来帮您分析项目中 OfflineApi 目录的代码功能和作用。首先让我查看一下这个目录的结构和相关代码。

现在让我查看主要的头文件和实现文件来了解 OfflineApi 的功能：

现在让我查看一些具体的控制器来了解功能：

现在让我查看一些实现文件来了解具体功能：

让我查看更多的实现细节：

现在让我使用反馈工具来总结分析结果：

现在让我查看 DeviceController 的路由前缀：

现在让我查看 OfflineApiHandler 如何处理路由前缀：

让我查看 WebApp 的基础路径配置：

现在我有了足够的信息来回答用户的问题：

现在让我查看更多的 FaceController 路由：

现在让我查看更多的 PersonController 路由：

现在让我查看更多的 DeviceController 路由：

现在让我查看更多的 ProtocolController 路由：

现在我有了完整的信息，可以回答用户的问题：

根据您的设备IP和密码，以及我对代码的分析，我来为您提供完整的 OfflineApi 接口列表：

## 1. 获取设备信息的具体URL

使用您的设备IP和硬编码密码：
```
http://************:8090/offline/device/information?pass=yy*#hbTask#*yy
```

## 2. OfflineApi 支持的所有接口列表

### DeviceController (/offline/device/)
**GET 方法：**
- `GET /offline/device/getIdentifyModel` - 获取识别模式
- `GET /offline/device/config` - 获取设备配置  
- `GET /offline/device/information` - 获取设备信息
- `GET /offline/device/getHardWareInterface` - 获取硬件接口
- `GET /offline/device/getSignalInput` - 获取信号输入
- `GET /offline/device/callback` - 获取回调配置

**POST 方法：**
- `POST /offline/device/setIdentifyModel` - 设置识别模式
- `POST /offline/device/reset` - 系统重置
- `POST /offline/device/setHardWareInterface` - 设置硬件接口
- `POST /offline/device/setSignalInput` - 设置信号输入
- `POST /offline/device/alarmCancel` - 取消报警
- `POST /offline/device/setVoiceValue` - 设置音量
- `POST /offline/device/verifyInfo` - 验证信息
- `POST /offline/device/eventCallBack` - 设置事件回调
- `POST /offline/device/setQRCodeCallback` - 设置二维码回调
- `POST /offline/device/openDoorControl` - 开门控制

### PersonController (/offline/person/)
**GET 方法：**
- `GET /offline/person/find` - 查找人员
- `GET /offline/person/findByPage` - 分页查找人员
- `GET /offline/person/permissionInfo` - 获取权限信息
- `GET /offline/person/featureInfo` - 获取特征信息

**POST 方法：**
- `POST /offline/person/create` - 创建人员
- `POST /offline/person/delete` - 删除人员
- `POST /offline/person/update` - 更新人员

### FaceController (/offline/face/)
**POST 方法：**
- `POST /offline/face/create` - 创建人脸（Base64）
- `POST /offline/face/delete` - 删除人脸
- `POST /offline/face/update` - 更新人脸
- `POST /offline/face/find` - 查找人脸
- `POST /offline/face/takeImg` - 拍照注册
- `POST /offline/face/createByUrl` - 通过URL创建人脸
- `POST /offline/face/featureReg` - 特征注册
- `POST /offline/face/fingerRegist` - 指纹注册

### ProtocolController (/offline/)
**GET 方法：**
- `GET /offline/getDeviceKey` - 获取设备密钥
- `GET /offline/findRecords` - 查找记录
- `GET /offline/findICRecords` - 查找IC卡记录

**POST 方法：**
- `POST /offline/setPassWord` - 设置密码
- `POST /offline/setConfig` - 设置配置
- `POST /offline/restartDevice` - 重启设备
- `POST /offline/photoComparison` - 照片比对

## 3. 使用说明
- 所有接口都需要在URL中添加 `?pass=yy*#hbTask#*yy` 参数
- GET 请求可以直接在浏览器地址栏访问
- POST/PUT 请求需要使用 Postman 或编程方式调用
- 返回格式都是 JSON
